interface Colors {
  red: string[];
  blue: string[];
  cyan: string[];
  green: string[];
  orange: string[];
  grey: string[];
}

const Theme = (colors: Colors) => {
  const { red, blue, green, orange, grey } = colors;
  const primaryColor = {
    50: "#E6F7FF",
    100: "#BAE7FF",
    200: "#91D5FF",
    300: "#69C0FF",
    400: "#40A9FF",
    500: "#1890FF", // main color - modern blue
    600: "#0960D7",
    700: "#0052CC",
    800: "#003A8C",
    900: "#002766",
    A100: "#BAE7FF",
    A200: "#91D5FF",
    A400: "#40A9FF",
    A700: "#0052CC",
    contrastText: "#FFFFFF",
  };

  const secondaryColor = {
    50: "#F3E5F5",
    100: "#E1BEE7",
    200: "#CE93D8",
    300: "#BA68C8",
    400: "#AB47BC",
    500: "#9C27B0", // main color - purple
    600: "#8E24AA",
    700: "#7B1FA2",
    800: "#6A1B9A",
    900: "#4A148C",
    A100: "#E1BEE7",
    A200: "#CE93D8",
    A400: "#AB47BC",
    A700: "#7B1FA2",
    contrastText: "#FFFFFF",
  };

  const greyColors = {
    0: grey[0],
    50: grey[1],
    100: grey[2],
    200: grey[3],
    300: grey[4],
    400: grey[5],
    500: grey[6],
    600: grey[7],
    700: grey[8],
    800: grey[9],
    900: grey[10],
    A50: grey[15],
    A100: grey[11],
    A200: grey[12],
    A400: grey[13],
    A700: grey[14],
    A800: grey[16],
  };
  const contrastText = "#fff";

  return {
    primary: {
      lighter: primaryColor[50],
      100: primaryColor[100],
      200: primaryColor[200],
      light: primaryColor[300],
      400: primaryColor[400],
      main: primaryColor[500],
      dark: primaryColor[600],
      700: primaryColor[700],
      darker: primaryColor[800],
      900: primaryColor[900],
      contrastText: primaryColor.contrastText,
    },
    secondary: {
      lighter: secondaryColor[50],
      100: secondaryColor[100],
      200: secondaryColor[200],
      light: secondaryColor[300],
      400: secondaryColor[400],
      main: secondaryColor[500],
      600: secondaryColor[600],
      dark: secondaryColor[700],
      800: secondaryColor[800],
      darker: secondaryColor[900],
      A100: secondaryColor[50],
      A200: secondaryColor[200],
      A400: secondaryColor[400],
      A700: secondaryColor[700],
      contrastText: secondaryColor.contrastText,
    },
    error: {
      lighter: red[50],
      light: red[300],
      main: red[500],
      dark: red[700],
      darker: red[900],
      contrastText,
    },
    warning: {
      lighter: orange[50],
      light: orange[300],
      main: orange[500],
      dark: orange[700],
      darker: orange[900],
      contrastText: greyColors[100],
    },
    info: {
      lighter: blue[50],
      light: blue[300],
      main: blue[500],
      dark: blue[700],
      darker: blue[900],
      contrastText,
    },
    success: {
      lighter: green[50],
      light: green[300],
      main: green[500],
      dark: green[700],
      darker: green[900],
      contrastText,
    },
    grey: greyColors,
  };
};

export default Theme;
