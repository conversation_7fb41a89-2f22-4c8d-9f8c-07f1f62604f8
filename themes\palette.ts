// material-ui
import { createTheme, PaletteMode } from "@mui/material/styles";
import { blue, cyan, green, orange, red } from "@mui/material/colors";

// project import
import ThemeOption from "./theme";

// ==============================|| DEFAULT THEME - PALETTE  ||============================== //

const Palette = (mode: PaletteMode) => {
  const greyPrimary = [
    "#ffffff",
    "#fafafa",
    "#f5f5f5",
    "#f0f0f0",
    "#d9d9d9",
    "#bfbfbf",
    "#8c8c8c",
    "#595959",
    "#262626",
    "#141414",
    "#000000",
  ];
  const greyAscent = ["#fafafa", "#bfbfbf", "#434343", "#1f1f1f"];
  const greyConstant = ["#fafafb", "#e6ebf1"];

  const greyColors = {
    ...greyPrimary,
    ...greyAscent,
    ...greyConstant,
  };

  const paletteColor = ThemeOption({
    grey: greyColors,
    blue: blue as any,
    red: red as any,
    orange: orange as any,
    cyan: cyan as any,
    green: green as any,
  });

  return createTheme({
    palette: {
      mode,
      common: {
        black: "#000",
        white: "#fff",
      },
      ...paletteColor,
      text: {
        primary: paletteColor.grey[700],
        secondary: paletteColor.grey[100],
        disabled: paletteColor.grey[400],
      },
      action: {
        disabled: paletteColor.grey[300],
      },
      divider: paletteColor.grey[50],
      background: {
        paper: paletteColor.grey[0],
        default: paletteColor.grey[0],
      },
    },
  });
};

export default Palette;
