// ==============================|| OVERRIDES - BUTTON ||============================== //

export default function Button(theme) {
    const disabledStyle = {
        '&.Mui-disabled': {
            backgroundColor: theme.palette.grey[100]
        }
    };

    return {
        MuiButton: {
            defaultProps: {
                disableElevation: true,
                disableRipple: true
            },
            styleOverrides: {
                root: {
                    fontWeight: 400
                },
                contained: {
                    ...disabledStyle
                },
                outlined: {
                    ...disabledStyle
                }
            }
        }
    };
}
