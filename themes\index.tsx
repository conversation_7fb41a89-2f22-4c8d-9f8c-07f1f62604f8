"use client";
import { useMemo } from "react";

// material-ui
import { CssBaseline, StyledEngineProvider } from "@mui/material";
import {
  BreakpointsOptions,
  createTheme,
  Direction,
  Mixins,
  ThemeProvider,
} from "@mui/material/styles";
// Supports weights 100-900
import "@fontsource-variable/vazirmatn";

// project import
import Palette from "./palette";
import Typography from "./typography";
import componentsOverride from "./overrides";
import { prefixer } from "stylis";
import rtlPlugin from "stylis-plugin-rtl";
import { AppRouterCacheProvider } from "@mui/material-nextjs/v15-appRouter";
import { TypographyStyleOptions } from "@mui/material/styles/createTypography";
// import { TypographyOptions } from "@mui/material/styles/createTypography";

interface ThemeCustomizationProps {
  children: React.ReactNode;
}
// ==============================|| DEFAULT THEME - MAIN  ||============================== //
export default function ThemeCustomization({
  children,
}: ThemeCustomizationProps) {
  const theme = Palette("light");

  const themeTypography = Typography({
    fontFamily: "'Vazirmatn Variable', sans-serif" as string,
  });

  const themeOptions = useMemo(() => {
    const safeTypography: TypographyStyleOptions = {
      ...themeTypography,
      fontFamily: "'Vazirmatn Variable', sans-serif", // Ensure this is a string, not an array
    };

    return {
      breakpoints: {
        values: {
          xs: 0,
          sm: 768,
          md: 1024,
          lg: 1266,
          xl: 1536,
        },
      } as BreakpointsOptions,
      direction: "rtl" as Direction,
      mixins: {
        toolbar: {
          minHeight: 60,
          paddingTop: 8,
          paddingBottom: 8,
        },
      } as Mixins,
      palette: theme.palette,
      typography: safeTypography,
      shadows: [
        "none",
        "rgba(0, 0, 0, 0.1) 0px 1px 2px 0px",
        "0 4px 8px rgba(0,0,0,0.05)",
        "0 8px 16px rgba(0,0,0,0.05)",
        "0 16px 32px rgba(0,0,0,0.05)",
        "0 32px 64px rgba(0,0,0,0.05)",
        "0 64px 128px rgba(0,0,0,0.05)",
        "0 128px 256px rgba(0,0,0,0.05)",
        "0 256px 512px rgba(0,0,0,0.05)",
        "0 512px 1024px rgba(0,0,0,0.05)",
        "0 1024px 2048px rgba(0,0,0,0.05)",
        "0 2048px 4096px rgba(0,0,0,0.05)",
        "0 4096px 8192px rgba(0,0,0,0.05)",
        "0 8192px 16384px rgba(0,0,0,0.05)",
        "0 16384px 32768px rgba(0,0,0,0.05)",
        "0 32768px 65536px rgba(0,0,0,0.05)",
        "0 65536px 131072px rgba(0,0,0,0.05)",
        "0 131072px 262144px rgba(0,0,0,0.05)",
        "0 262144px 524288px rgba(0,0,0,0.05)",
        "0 524288px 1048576px rgba(0,0,0,0.05)",
        "0 1048576px 2097152px rgba(0,0,0,0.05)",
        "0 2097152px 4194304px rgba(0,0,0,0.05)",
        "0 4194304px 8388608px rgba(0,0,0,0.05)",
        "0 8388608px 16777216px rgba(0,0,0,0.05)",
        "0 16777216px 33554432px rgba(0,0,0,0.05)",
      ] as [
        "none",
        string,
        string,
        string,
        string,
        string,
        string,
        string,
        string,
        string,
        string,
        string,
        string,
        string,
        string,
        string,
        string,
        string,
        string,
        string,
        string,
        string,
        string,
        string,
        string
      ],
    };
  }, [theme, themeTypography]);

  const themes = createTheme({
    ...themeOptions,
    typography: themeOptions.typography as any,
  });
  themes.components = componentsOverride(themes);

  return (
    <StyledEngineProvider injectFirst>
      <AppRouterCacheProvider
        options={{
          key: "muirtl",
          stylisPlugins: [prefixer, rtlPlugin],
        }}
      >
        <ThemeProvider theme={themes}>
          <CssBaseline />
          {children}
        </ThemeProvider>
      </AppRouterCacheProvider>
    </StyledEngineProvider>
  );
}
